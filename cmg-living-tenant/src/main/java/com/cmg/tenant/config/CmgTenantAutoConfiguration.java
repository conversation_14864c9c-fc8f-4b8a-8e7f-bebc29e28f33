package com.cmg.tenant.config;


import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.cmg.tenant.core.aop.TenantIgnoreAspect;
import com.cmg.tenant.core.db.TenantDatabaseInterceptor;
import com.cmg.tenant.core.util.MyBatisUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;

import javax.annotation.Resource;

@AutoConfiguration
@ConditionalOnProperty(prefix = "cmg.tenant", value = "enable", matchIfMissing = true) // 允许使用 cmg.tenant.enable=false 禁用多租户
@EnableConfigurationProperties(TenantProperties.class)
public class CmgTenantAutoConfiguration {

    @Resource
    private ApplicationContext applicationContext;

    // ========== AOP ==========

    @Bean
    public TenantIgnoreAspect tenantIgnoreAspect() {
        return new TenantIgnoreAspect();
    }

    // ========== DB ==========

    @Bean
    public TenantLineInnerInterceptor tenantLineInnerInterceptor(TenantProperties properties,
                                                                 MybatisPlusInterceptor interceptor) {
        TenantLineInnerInterceptor inner = new TenantLineInnerInterceptor(new TenantDatabaseInterceptor(properties));
        // 添加到 interceptor 中
        // 需要加在首个，主要是为了在分页插件前面。这个是 MyBatis Plus 的规定
        MyBatisUtils.addInterceptor(interceptor, inner, 0);
        return inner;
    }

}
